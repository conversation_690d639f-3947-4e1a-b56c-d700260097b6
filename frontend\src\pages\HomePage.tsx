import { useState, useEffect } from 'react'
import { apiService } from '../services/api'

const HomePage = () => {
  const [data, setData] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        const response = await apiService.get('/health')
        setData(response)
      } catch (err) {
        setError('Errore nel caricamento dei dati')
        console.error('Error fetching data:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  return (
    <div className="px-4 py-6 sm:px-0">
      <div className="border-4 border-dashed border-gray-200 rounded-lg p-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Benvenuto nell'Applicazione Multilivello
          </h1>
          <p className="text-lg text-gray-600 mb-8">
            Questa è un'applicazione di esempio che dimostra un'architettura multilivello
            con React, Node.js e PostgreSQL.
          </p>
          
          <div className="bg-white shadow rounded-lg p-6 max-w-md mx-auto">
            <h2 className="text-xl font-semibold mb-4">Stato del Backend</h2>
            {loading && (
              <div className="text-blue-600">Caricamento...</div>
            )}
            {error && (
              <div className="text-red-600">{error}</div>
            )}
            {data && (
              <div className="text-green-600">
                ✅ Backend connesso: {data.message || 'OK'}
              </div>
            )}
          </div>

          <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-blue-50 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-blue-900 mb-2">
                Frontend
              </h3>
              <p className="text-blue-700">
                React 18 + TypeScript + Vite
              </p>
            </div>
            <div className="bg-green-50 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-green-900 mb-2">
                Backend
              </h3>
              <p className="text-green-700">
                Node.js + Express + TypeScript
              </p>
            </div>
            <div className="bg-purple-50 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-purple-900 mb-2">
                Database
              </h3>
              <p className="text-purple-700">
                PostgreSQL + Prisma ORM
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default HomePage
