# Applicazione Multilivello Standard

Questo progetto implementa un'architettura multilivello moderna con:

## Architettura

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Presentation  │    │   Business      │    │   Data Access   │
│     Layer       │◄──►│     Logic       │◄──►│     Layer       │
│   (Frontend)    │    │   (Backend)     │    │   (Database)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Stack Tecnologico

- **Frontend**: React 18 + Vite + TypeScript
- **Backend**: Node.js + Express + TypeScript
- **Database**: PostgreSQL + Prisma ORM
- **Autenticazione**: JWT + bcrypt
- **Containerizzazione**: Docker + Docker Compose
- **Testing**: Jest + React Testing Library

## Struttura del Progetto

```
/
├── frontend/           # React application
├── backend/            # Node.js API server
├── database/           # Database scripts and migrations
├── shared/             # Shared types and utilities
├── docker/             # Docker configurations
├── docs/               # Documentation
└── scripts/            # Build and deployment scripts
```

## Quick Start

1. Clona il repository
2. Installa le dipendenze: `npm run install:all`
3. Avvia i servizi: `docker-compose up -d`
4. Avvia l'applicazione: `npm run dev`

## Sviluppo

- Frontend: http://localhost:5173
- Backend API: http://localhost:3000
- Database: localhost:5432

Per maggiori dettagli, consulta la documentazione in `/docs/`.
