const AboutPage = () => {
  return (
    <div className="px-4 py-6 sm:px-0">
      <div className="border-4 border-dashed border-gray-200 rounded-lg p-8">
        <div className="max-w-3xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            Informazioni sull'Applicazione
          </h1>
          
          <div className="prose prose-lg text-gray-600">
            <p className="mb-6">
              Questa applicazione dimostra un'architettura multilivello moderna
              utilizzando le migliori pratiche di sviluppo software.
            </p>

            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              Architettura
            </h2>
            <ul className="list-disc pl-6 mb-6">
              <li><strong>Presentation Layer:</strong> React con TypeScript per un'interfaccia utente moderna e type-safe</li>
              <li><strong>Business Logic Layer:</strong> Node.js con Express per la logica di business e API REST</li>
              <li><strong>Data Access Layer:</strong> PostgreSQL con Prisma ORM per la persistenza dei dati</li>
            </ul>

            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              Tecnologie Utilizzate
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <h3 className="text-lg font-semibold mb-2">Frontend</h3>
                <ul className="list-disc pl-6">
                  <li>React 18</li>
                  <li>TypeScript</li>
                  <li>Vite</li>
                  <li>React Router</li>
                  <li>TanStack Query</li>
                  <li>Tailwind CSS</li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-2">Backend</h3>
                <ul className="list-disc pl-6">
                  <li>Node.js</li>
                  <li>Express</li>
                  <li>TypeScript</li>
                  <li>Prisma ORM</li>
                  <li>JWT Authentication</li>
                  <li>bcrypt</li>
                </ul>
              </div>
            </div>

            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              Caratteristiche
            </h2>
            <ul className="list-disc pl-6">
              <li>Architettura scalabile e modulare</li>
              <li>Type safety end-to-end con TypeScript</li>
              <li>API REST ben strutturate</li>
              <li>Gestione dello stato con React Query</li>
              <li>Routing client-side</li>
              <li>Containerizzazione con Docker</li>
              <li>Testing automatizzato</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AboutPage
