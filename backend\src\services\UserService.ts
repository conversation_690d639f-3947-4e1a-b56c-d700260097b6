import { User } from './AuthService'

export interface UpdateUserData {
  name?: string
  email?: string
}

export class UserService {
  // Mock data store - replace with actual database
  private users: User[] = []

  async findUserById(id: string): Promise<User | null> {
    // Mock implementation
    const user = this.users.find(u => u.id === id)
    return user || null
  }

  async updateUser(id: string, updateData: UpdateUserData): Promise<User> {
    // Mock implementation
    const userIndex = this.users.findIndex(u => u.id === id)
    if (userIndex === -1) {
      throw new Error('User not found')
    }

    const user = this.users[userIndex]!
    const updatedUser: User = {
      ...user,
      ...updateData,
      updatedAt: new Date(),
    }

    this.users[userIndex] = updatedUser
    return updatedUser
  }

  async getAllUsers(): Promise<User[]> {
    // Mock implementation
    return this.users
  }

  async deleteUser(id: string): Promise<void> {
    // Mock implementation
    const userIndex = this.users.findIndex(u => u.id === id)
    if (userIndex === -1) {
      throw new Error('User not found')
    }

    this.users.splice(userIndex, 1)
  }
}
