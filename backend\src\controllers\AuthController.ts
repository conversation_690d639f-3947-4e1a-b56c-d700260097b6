import { Request, Response, NextFunction } from 'express'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { createError } from '../middleware/errorHandler'
import { AuthService } from '../services/AuthService'

export class AuthController {
  private authService: AuthService

  constructor() {
    this.authService = new AuthService()
  }

  register = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { email, password, name } = req.body

      // Validate input
      if (!email || !password || !name) {
        return next(createError('Email, password, and name are required', 400))
      }

      // Check if user already exists
      const existingUser = await this.authService.findUserByEmail(email)
      if (existingUser) {
        return next(createError('User already exists with this email', 409))
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 12)

      // Create user
      const user = await this.authService.createUser({
        email,
        password: hashedPassword,
        name,
      })

      // Generate token
      const token = this.generateToken(user.id, user.email)

      res.status(201).json({
        success: true,
        message: 'User registered successfully',
        data: {
          user: {
            id: user.id,
            email: user.email,
            name: user.name,
          },
          token,
        },
      })
    } catch (error) {
      next(error)
    }
  }

  login = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { email, password } = req.body

      // Validate input
      if (!email || !password) {
        return next(createError('Email and password are required', 400))
      }

      // Find user
      const user = await this.authService.findUserByEmail(email)
      if (!user) {
        return next(createError('Invalid credentials', 401))
      }

      // Check password
      const isPasswordValid = await bcrypt.compare(password, user.password)
      if (!isPasswordValid) {
        return next(createError('Invalid credentials', 401))
      }

      // Generate token
      const token = this.generateToken(user.id, user.email)

      res.json({
        success: true,
        message: 'Login successful',
        data: {
          user: {
            id: user.id,
            email: user.email,
            name: user.name,
          },
          token,
        },
      })
    } catch (error) {
      next(error)
    }
  }

  refreshToken = async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Implementation for token refresh
      res.json({
        success: true,
        message: 'Token refresh not implemented yet',
      })
    } catch (error) {
      next(error)
    }
  }

  private generateToken(userId: string, email: string): string {
    const jwtSecret = process.env.JWT_SECRET
    if (!jwtSecret) {
      throw new Error('JWT secret not configured')
    }

    return jwt.sign(
      { id: userId, email },
      jwtSecret,
      { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    )
  }
}
