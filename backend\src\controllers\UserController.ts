import { Response, NextFunction } from 'express'
import { AuthenticatedRequest } from '../middleware/auth'
import { createError } from '../middleware/errorHandler'
import { UserService } from '../services/UserService'

export class UserController {
  private userService: UserService

  constructor() {
    this.userService = new UserService()
  }

  getProfile = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return next(createError('User not authenticated', 401))
      }

      const user = await this.userService.findUserById(req.user.id)
      if (!user) {
        return next(createError('User not found', 404))
      }

      res.json({
        success: true,
        data: {
          user: {
            id: user.id,
            email: user.email,
            name: user.name,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
          },
        },
      })
    } catch (error) {
      next(error)
    }
  }

  updateProfile = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return next(createError('User not authenticated', 401))
      }

      const { name } = req.body
      if (!name) {
        return next(createError('Name is required', 400))
      }

      const updatedUser = await this.userService.updateUser(req.user.id, { name })

      res.json({
        success: true,
        message: 'Profile updated successfully',
        data: {
          user: {
            id: updatedUser.id,
            email: updatedUser.email,
            name: updatedUser.name,
            updatedAt: updatedUser.updatedAt,
          },
        },
      })
    } catch (error) {
      next(error)
    }
  }

  getAllUsers = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const users = await this.userService.getAllUsers()

      res.json({
        success: true,
        data: {
          users: users.map(user => ({
            id: user.id,
            email: user.email,
            name: user.name,
            createdAt: user.createdAt,
          })),
        },
      })
    } catch (error) {
      next(error)
    }
  }

  getUserById = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params

      const user = await this.userService.findUserById(id)
      if (!user) {
        return next(createError('User not found', 404))
      }

      res.json({
        success: true,
        data: {
          user: {
            id: user.id,
            email: user.email,
            name: user.name,
            createdAt: user.createdAt,
          },
        },
      })
    } catch (error) {
      next(error)
    }
  }
}
