{"name": "backend", "version": "1.0.0", "description": "Backend API per applicazione multilivello", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:seed": "ts-node prisma/seed.ts", "db:studio": "prisma studio"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.9.2", "@prisma/client": "^5.2.0", "compression": "^1.7.4", "express-rate-limit": "^6.10.0"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/morgan": "^1.9.4", "@types/bcryptjs": "^2.4.2", "@types/jsonwebtoken": "^9.0.2", "@types/compression": "^1.7.2", "@types/node": "^20.5.0", "@typescript-eslint/eslint-plugin": "^6.4.0", "@typescript-eslint/parser": "^6.4.0", "eslint": "^8.47.0", "jest": "^29.6.2", "@types/jest": "^29.5.4", "ts-jest": "^29.1.1", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "typescript": "^5.1.6", "prisma": "^5.2.0", "supertest": "^6.3.3", "@types/supertest": "^2.0.12"}, "keywords": ["nodejs", "express", "typescript", "api", "rest"], "author": "", "license": "MIT"}