-- Database initialization script
-- This script creates the database and sets up initial configuration

-- Create database (run this as superuser)
-- CREATE DATABASE app_db;

-- Create application user
-- CREATE USER app_user WITH PASSWORD 'your_password_here';

-- Grant privileges
-- GRANT ALL PRIVILEGES ON DATABASE app_db TO app_user;

-- Connect to the database
\c app_db;

-- Enable extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create schemas if needed
-- CREATE SCHEMA IF NOT EXISTS app_schema;

-- Set default privileges
-- ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO app_user;
-- ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO app_user;
-- ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO app_user;
