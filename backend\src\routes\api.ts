import { Router } from 'express'
import userRoutes from './users'
import authRoutes from './auth'

const router = Router()

// API routes
router.use('/auth', authRoutes)
router.use('/users', userRoutes)

// API info endpoint
router.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'API is running',
    version: '1.0.0',
    endpoints: {
      auth: '/api/auth',
      users: '/api/users',
      health: '/health',
    },
  })
})

export default router
