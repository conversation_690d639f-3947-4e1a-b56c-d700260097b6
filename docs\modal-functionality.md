# Funzionalità Modal "Ciao!"

Questa documentazione descrive la funzionalità del modal che mostra il messaggio "Ciao!" implementata nell'applicazione.

## Componenti Implementati

### 1. Modal Component (`frontend/src/components/Modal.tsx`)

Un componente modale riutilizzabile con le seguenti caratteristiche:

- **Backdrop**: Sfondo scuro che si chiude al click
- **Escape Key**: Chiusura con il tasto ESC
- **Dimensioni configurabili**: sm, md, lg, xl
- **Prevenzione scroll**: Blocca lo scroll del body quando aperto
- **Accessibilità**: Focus management e ARIA attributes

#### Props:
```typescript
interface ModalProps {
  isOpen: boolean
  onClose: () => void
  title?: string
  children: ReactNode
  size?: 'sm' | 'md' | 'lg' | 'xl'
}
```

### 2. useModal Hook (`frontend/src/hooks/useModal.ts`)

Hook personalizzato per gestire lo stato del modale:

```typescript
const { isOpen, openModal, closeModal, toggleModal } = useModal()
```

## Implementazioni

### 1. Pulsante nella Homepage

- **Posizione**: Sezione centrale della homepage
- **Stile**: Pulsante blu con icona emoji
- **Messaggio**: "Ciao!" con emoji di saluto
- **Contenuto**: Messaggio di benvenuto personalizzato

### 2. Pulsante nella Navbar

- **Posizione**: Angolo superiore destro della navbar
- **Stile**: Pulsante compatto blu
- **Messaggio**: "Ciao!" con emoji festa
- **Contenuto**: Messaggio che spiega l'origine del click

## Utilizzo

### Aprire un Modal

```tsx
import Modal from '../components/Modal'
import { useModal } from '../hooks/useModal'

const MyComponent = () => {
  const { isOpen, openModal, closeModal } = useModal()

  return (
    <>
      <button onClick={openModal}>
        Apri Modal
      </button>
      
      <Modal
        isOpen={isOpen}
        onClose={closeModal}
        title="Titolo del Modal"
        size="md"
      >
        <p>Contenuto del modal...</p>
      </Modal>
    </>
  )
}
```

### Personalizzazione

Il modal può essere personalizzato modificando:

- **Dimensioni**: Cambiare la prop `size`
- **Stili**: Modificare le classi Tailwind CSS
- **Animazioni**: Aggiungere transizioni CSS
- **Contenuto**: Inserire qualsiasi JSX nel children

## Caratteristiche Tecniche

### Accessibilità
- Gestione del focus
- Chiusura con ESC
- Click outside per chiudere
- Prevenzione scroll del body

### Responsive Design
- Adattivo su tutti i dispositivi
- Dimensioni flessibili
- Padding appropriato per mobile

### Performance
- Rendering condizionale
- Event listeners ottimizzati
- Cleanup automatico degli effetti

## Esempi di Estensione

### Modal di Conferma
```tsx
<Modal isOpen={isOpen} onClose={closeModal} title="Conferma">
  <p>Sei sicuro di voler procedere?</p>
  <div className="flex gap-2 mt-4">
    <button onClick={handleConfirm}>Conferma</button>
    <button onClick={closeModal}>Annulla</button>
  </div>
</Modal>
```

### Modal con Form
```tsx
<Modal isOpen={isOpen} onClose={closeModal} title="Nuovo Utente" size="lg">
  <form onSubmit={handleSubmit}>
    <input type="text" placeholder="Nome" />
    <input type="email" placeholder="Email" />
    <button type="submit">Salva</button>
  </form>
</Modal>
```

## Note di Sviluppo

- Il modal utilizza Tailwind CSS per gli stili
- È compatibile con React 18+
- Supporta TypeScript out of the box
- Può essere facilmente esteso per nuove funzionalità
