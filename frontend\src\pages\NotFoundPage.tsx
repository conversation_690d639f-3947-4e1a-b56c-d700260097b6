import { Link } from 'react-router-dom'

const NotFoundPage = () => {
  return (
    <div className="px-4 py-6 sm:px-0">
      <div className="text-center">
        <div className="max-w-md mx-auto">
          <div className="text-6xl font-bold text-gray-300 mb-4">404</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Pagina non trovata
          </h1>
          <p className="text-gray-600 mb-8">
            La pagina che stai cercando non esiste o è stata spostata.
          </p>
          <Link
            to="/"
            className="inline-flex items-center px-4 py-2 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Torna alla Home
          </Link>
        </div>
      </div>
    </div>
  )
}

export default NotFoundPage
