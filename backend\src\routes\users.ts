import { Router } from 'express'
import { UserController } from '../controllers/UserController'
import { authenticateToken } from '../middleware/auth'

const router = Router()
const userController = new UserController()

// Protected routes
router.use(authenticateToken)

router.get('/profile', userController.getProfile)
router.put('/profile', userController.updateProfile)
router.get('/', userController.getAllUsers)
router.get('/:id', userController.getUserById)

export default router
