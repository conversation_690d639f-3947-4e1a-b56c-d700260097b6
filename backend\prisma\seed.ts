import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seeding...')

  // Create demo users
  const hashedPassword = await bcrypt.hash('password123', 12)

  const user1 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: hashedPassword,
      name: 'Admin User',
    },
  })

  const user2 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: hashedPassword,
      name: 'Regular User',
    },
  })

  // Create demo posts
  const post1 = await prisma.post.create({
    data: {
      title: 'Benvenuto nell\'applicazione multilivello',
      content: 'Questo è un post di esempio che dimostra le funzionalità dell\'applicazione.',
      published: true,
      authorId: user1.id,
    },
  })

  const post2 = await prisma.post.create({
    data: {
      title: 'Architettura moderna',
      content: 'Questa applicazione utilizza React, Node.js e PostgreSQL per creare un\'architettura robusta e scalabile.',
      published: true,
      authorId: user2.id,
    },
  })

  // Create demo comments
  await prisma.comment.create({
    data: {
      content: 'Ottimo lavoro! L\'architettura sembra molto ben strutturata.',
      authorId: user2.id,
      postId: post1.id,
    },
  })

  await prisma.comment.create({
    data: {
      content: 'Sono d\'accordo, l\'uso di TypeScript rende tutto più sicuro.',
      authorId: user1.id,
      postId: post2.id,
    },
  })

  console.log('✅ Database seeding completed!')
  console.log(`👤 Created users: ${user1.email}, ${user2.email}`)
  console.log(`📝 Created posts: "${post1.title}", "${post2.title}"`)
  console.log('💬 Created sample comments')
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
