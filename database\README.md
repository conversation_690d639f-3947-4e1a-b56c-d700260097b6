# Database Configuration

Questa directory contiene la configurazione e gli script per il database PostgreSQL.

## Struttura

```
database/
├── scripts/
│   └── init.sql          # Script di inizializzazione database
├── migrations/           # Migrazioni personalizzate (se necessarie)
└── README.md            # Questa documentazione
```

## Setup Database

### 1. Installazione PostgreSQL

#### Con Docker (Raccomandato)
```bash
docker run --name postgres-app \
  -e POSTGRES_DB=app_db \
  -e POSTGRES_USER=app_user \
  -e POSTGRES_PASSWORD=your_password \
  -p 5432:5432 \
  -d postgres:15
```

#### Installazione Locale
- Scarica e installa PostgreSQL dal sito ufficiale
- Crea un database chiamato `app_db`
- Crea un utente `app_user` con password

### 2. Configurazione Environment

Copia il file `.env.example` in `.env` nel backend e configura:

```env
DATABASE_URL="postgresql://app_user:your_password@localhost:5432/app_db?schema=public"
```

### 3. Esecuzione Migrazioni

```bash
cd backend
npm run db:generate  # Genera il client Prisma
npm run db:migrate   # Esegue le migrazioni
npm run db:seed      # Popola il database con dati di esempio
```

### 4. Gestione Database

```bash
# Visualizza il database con Prisma Studio
npm run db:studio

# Reset del database (attenzione: cancella tutti i dati)
npx prisma migrate reset

# Backup del database
pg_dump -h localhost -U app_user app_db > backup.sql

# Restore del database
psql -h localhost -U app_user app_db < backup.sql
```

## Schema Database

Il database utilizza Prisma ORM con il seguente schema:

- **users**: Utenti dell'applicazione
- **posts**: Post/articoli creati dagli utenti
- **comments**: Commenti sui post

Vedi `backend/prisma/schema.prisma` per i dettagli completi dello schema.
